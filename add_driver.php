<?php
require_once 'db_connect.php';

// Get all available rickshaws
$sql = "SELECT r.id, r.rickshaw_number 
        FROM rickshaws r 
        LEFT JOIN drivers d ON r.id = d.rickshaw_id 
        WHERE d.id IS NULL AND r.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $name = $_POST['name'];
    $phone = $_POST['phone'];
    $nid = $_POST['nid'];
    $rickshaw_id = $_POST['rickshaw_id'];
    $rent = $_POST['rent'];
    $join_date = $_POST['join_date'];
    
    $sql = "INSERT INTO drivers (user_id, name, phone, nid, rickshaw_id, rent, join_date) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("isssids", $_SESSION['user_id'], $name, $phone, $nid, $rickshaw_id, $rent, $join_date);
    
    if ($stmt->execute()) {
        header("Location: index.php");
        exit();
    } else {
        $error = "Error: " . $stmt->error;
    }
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>নতুন ড্রাইভার যোগ করুন</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --purple-50: #f5f3fa;
            --purple-100: #ede9fe;
            --purple-200: #c4b5fd;
            --purple-300: #a78bfa;
            --purple-400: #8b5cf6;
            --purple-500: #7c3aed;
            --purple-600: #6d28d9;
            --purple-700: #3a006a;
            --purple-800: #2d003e;
            --purple-900: #1e0026;
            --purple-950: #12001a;
            --white: #fff;
        }
        body {
            font-family: 'Noto Sans Bengali', 'SolaimanLipi', Arial, sans-serif;
            background: linear-gradient(135deg, var(--purple-700) 0%, var(--purple-800) 100%);
            color: var(--white);
            padding-top: 70px;
        }
        .main-navbar {
            background-color: var(--purple-700);
            box-shadow: 0 4px 6px -1px rgba(58, 0, 106, 0.1), 0 2px 4px -1px rgba(58, 0, 106, 0.06);
            padding: 0.75rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1030;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--white);
        }
        .navbar-brand:hover {
            color: var(--purple-200);
        }
        .footer {
            background-color: var(--purple-900);
            color: var(--purple-200);
            padding: 1.5rem 0;
            text-align: center;
            margin-top: 3rem;
            border-top: 1px solid var(--purple-700);
        }
        .footer p {
            margin-bottom: 0;
            font-weight: 500;
        }
        .developer-credit {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: var(--purple-400);
        }
        .card {
            border: none;
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.2s;
            background: linear-gradient(135deg, var(--purple-800) 60%, var(--purple-700) 100%);
            color: var(--white);
            margin-bottom: 1.5rem;
        }
        .card-header {
            padding: 1rem 1.25rem;
            border: none;
            font-weight: 600;
            background: linear-gradient(135deg, var(--purple-700), var(--purple-900));
            color: var(--white);
        }
        .btn-primary {
            background: linear-gradient(135deg, var(--purple-700), var(--purple-800));
            border-color: var(--purple-800);
            color: var(--white);
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, var(--purple-800), var(--purple-700));
            border-color: var(--purple-900);
        }
        .btn-secondary {
            background: linear-gradient(135deg, var(--purple-800), var(--purple-700));
            border-color: var(--purple-900);
            color: var(--white);
        }
        .btn-secondary:hover {
            background: linear-gradient(135deg, var(--purple-700), var(--purple-800));
            border-color: var(--purple-900);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.3);
            color: #fbbf24;
            border-radius: 0.5rem;
        }

        .alert-warning .btn-primary {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            border: none;
            font-size: 0.875rem;
        }

        .alert-warning .btn-primary:hover {
            background: linear-gradient(135deg, #d97706, #f59e0b);
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <nav class="navbar main-navbar">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                প্রচেষ্টা অটো রিকশা
            </a>
        </div>
    </nav>
    <div class="container mt-4" style="max-width: 500px;">
        <div class="card">
            <div class="card-header text-center">
                <i class="bi bi-person-plus-fill me-2"></i> নতুন ড্রাইভার যোগ করুন
            </div>
            <div class="card-body">
        
        <?php if(isset($error)) { ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php } ?>
        <form method="post" action="">
            <div class="mb-3">
                <label for="name" class="form-label">নাম</label>
                <input type="text" class="form-control" id="name" name="name" required>
            </div>
            
            <div class="mb-3">
                <label for="phone" class="form-label">ফোন নম্বর</label>
                <input type="text" class="form-control" id="phone" name="phone" required>
            </div>
            
            <div class="mb-3">
                <label for="nid" class="form-label">এনআইডি নম্বর</label>
                <input type="text" class="form-control" id="nid" name="nid" required>
            </div>
            
            <div class="mb-3">
                <label for="rickshaw_id" class="form-label">রিকশা নম্বর</label>
                <select class="form-control" id="rickshaw_id" name="rickshaw_id" required>
                    <option value="">রিকশা নির্বাচন করুন</option>
                    <?php
                    if ($result->num_rows > 0) {
                        while($row = $result->fetch_assoc()) {
                            $selected = "";
                            if(isset($_GET['rickshaw_id']) && $_GET['rickshaw_id'] == $row['id']) {
                                $selected = "selected";
                            }
                            echo "<option value='".$row["id"]."' ".$selected.">রিকশা #".$row["rickshaw_number"]."</option>";
                        }
                    }
                    ?>
                </select>
                <?php if ($result->num_rows == 0): ?>
                    <div class="alert alert-warning mt-2">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>কোন উপলব্ধ রিকশা নেই!</strong><br>
                        প্রথমে একটি রিকশা যোগ করুন, তারপর ড্রাইভার যোগ করুন।
                        <div class="mt-2">
                            <a href="add_rickshaw.php" class="btn btn-sm btn-primary">
                                <i class="bi bi-plus-circle me-1"></i>নতুন রিকশা যোগ করুন
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="mb-3">
                <label for="rent" class="form-label">ভাড়া (টাকা)</label>
                <input type="number" class="form-control" id="rent" name="rent" required>
            </div>
            
            <div class="mb-3">
                <label for="join_date" class="form-label">যোগদানের তারিখ</label>
                <input type="date" class="form-control" id="join_date" name="join_date" required>
            </div>
            
            <button type="submit" class="btn btn-primary" <?php echo ($result->num_rows == 0) ? 'disabled' : ''; ?>>
                <?php echo ($result->num_rows == 0) ? 'প্রথমে রিকশা যোগ করুন' : 'সংরক্ষণ করুন'; ?>
            </button>
            <a href="index.php" class="btn btn-secondary">বাতিল করুন</a>
        </form>
            </div>
        </div>
        <div class="footer">
            <p>© <?php echo date('Y'); ?> প্রচেষ্টা অটো রিকশা। সর্বস্বত্ব সংরক্ষিত।</p>
            <p class="developer-credit"> ©মাহতাব উদ্দিন আহমেদ।</p>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>