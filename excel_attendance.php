<?php
session_start();
require_once 'db_connect.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get current month and year
$currentMonth = isset($_GET['month']) ? intval($_GET['month']) : intval(date('m'));
$currentYear = isset($_GET['year']) ? intval($_GET['year']) : intval(date('Y'));

// Validate month and year
if ($currentMonth < 1 || $currentMonth > 12) {
    $currentMonth = intval(date('m'));
}
if ($currentYear < 2023 || $currentYear > 2100) {
    $currentYear = intval(date('Y'));
}

// Get days in month
$daysInMonth = cal_days_in_month(CAL_GREGORIAN, $currentMonth, $currentYear);

// Get all rickshaws with drivers (both active and inactive)
$sql = "SELECT r.id as rickshaw_id, r.rickshaw_number, 
               d.id as driver_id, d.name as driver_name
        FROM rickshaws r 
        JOIN drivers d ON r.id = d.rickshaw_id 
        WHERE r.user_id = ?
        ORDER BY r.rickshaw_number";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();

$rickshaws = [];
if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $rickshaws[] = $row;
    }
}

// Process form submission for updating rent
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_rent'])) {
    $driver_id = intval($_POST['driver_id']);
    $day = intval($_POST['day']);
    $rent = floatval($_POST['rent']);

    // Validate and construct the date properly
    $currentMonth = isset($_GET['month']) ? intval($_GET['month']) : date('m');
    $currentYear = isset($_GET['year']) ? intval($_GET['year']) : date('Y');

    // If date is provided in POST, use it; otherwise construct from day
    if (!empty($_POST['date']) && $_POST['date'] !== '') {
        $date = $_POST['date'];
    } else {
        // Construct date from current month, year and day
        $date = sprintf("%04d-%02d-%02d", $currentYear, $currentMonth, $day);
    }

    // Validate date format
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
        // If date format is invalid, construct it properly
        $date = sprintf("%04d-%02d-%02d", $currentYear, $currentMonth, $day);
    }

    // Additional validation - check if date is valid
    $dateTime = DateTime::createFromFormat('Y-m-d', $date);
    if (!$dateTime || $dateTime->format('Y-m-d') !== $date) {
        // Invalid date, redirect with error
        header("Location: excel_attendance.php?month=$currentMonth&year=$currentYear&error=invalid_date");
        exit();
    }

    try {
        // Check if attendance record exists, if not create it
        $checkSql = "SELECT id FROM attendance WHERE driver_id = ? AND user_id = ? AND date = ?";
        $stmt = $conn->prepare($checkSql);
        $stmt->bind_param("iis", $driver_id, $_SESSION['user_id'], $date);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            // Update existing record
            $updateSql = "UPDATE attendance SET rent = ? WHERE driver_id = ? AND user_id = ? AND date = ?";
            $stmt = $conn->prepare($updateSql);
            $stmt->bind_param("diis", $rent, $driver_id, $_SESSION['user_id'], $date);
            $stmt->execute();
        } else {
            // Create new attendance record with 'উপস্থিত' status
            $insertSql = "INSERT INTO attendance (user_id, driver_id, date, status, rent) VALUES (?, ?, ?, 'উপস্থিত', ?)";
            $stmt = $conn->prepare($insertSql);
            $stmt->bind_param("iisd", $_SESSION['user_id'], $driver_id, $date, $rent);
            $stmt->execute();
        }

        // Redirect to refresh the page
        header("Location: excel_attendance.php?month=$currentMonth&year=$currentYear&updated=1");
        exit();

    } catch (Exception $e) {
        // Handle any database errors
        header("Location: excel_attendance.php?month=$currentMonth&year=$currentYear&error=database_error");
        exit();
    }
}

// Process form submission for updating attendance
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_attendance'])) {
    foreach ($_POST['attendance'] as $driver_id => $days) {
        foreach ($days as $day => $status) {
            $date = "$currentYear-$currentMonth-$day";
            $rent = isset($_POST['rent'][$driver_id][$day]) ? $_POST['rent'][$driver_id][$day] : 0;
            
            // Check if record exists
            $checkSql = "SELECT id FROM attendance WHERE driver_id = ? AND user_id = ? AND date = ?";
            $stmt = $conn->prepare($checkSql);
            $stmt->bind_param("iis", $driver_id, $_SESSION['user_id'], $date);
            $stmt->execute();
            $checkResult = $stmt->get_result();
            
            if ($checkResult->num_rows > 0) {
                // Update existing record
                $attendanceId = $checkResult->fetch_assoc()['id'];
                $updateSql = "UPDATE attendance SET status = ?, rent = ? WHERE id = ? AND user_id = ?";
                $stmt = $conn->prepare($updateSql);
                $stmt->bind_param("sdii", $status, $rent, $attendanceId, $_SESSION['user_id']);
                $stmt->execute();
            } else if ($status != '') {
                // Insert new record
                $insertSql = "INSERT INTO attendance (user_id, driver_id, date, status, rent) VALUES (?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($insertSql);
                $stmt->bind_param("iissd", $_SESSION['user_id'], $driver_id, $date, $status, $rent);
                $stmt->execute();
            }
        }
    }
    
    // Redirect to refresh the page
    header("Location: excel_attendance.php?month=$currentMonth&year=$currentYear&updated=1");
    exit();
}

// Get attendance data for all drivers for the selected month
$attendanceData = [];
foreach ($rickshaws as $rickshaw) {
    $driver_id = $rickshaw['driver_id'];
    
    $attendanceSql = "SELECT date, status, rent FROM attendance 
                     WHERE driver_id = ? AND user_id = ? AND MONTH(date) = ? AND YEAR(date) = ?";
    $stmt = $conn->prepare($attendanceSql);
    $stmt->bind_param("iiii", $driver_id, $_SESSION['user_id'], $currentMonth, $currentYear);
    $stmt->execute();
    $attendanceResult = $stmt->get_result();
    
    while($row = $attendanceResult->fetch_assoc()) {
        $day = date('j', strtotime($row['date']));
        $attendanceData[$driver_id][$day] = [
            'status' => $row['status'],
            'rent' => $row['rent']
        ];
    }
}

// Get default rent amount
$defaultRentSql = "SELECT amount FROM rent_settings WHERE day_type = 'regular' AND user_id = ? LIMIT 1";
$stmt = $conn->prepare($defaultRentSql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$defaultRentResult = $stmt->get_result();
$defaultRent = 150;
if ($defaultRentResult && $defaultRentResult->num_rows > 0) {
    $defaultRent = $defaultRentResult->fetch_assoc()['amount'];
}

// Get Friday rent amount
$fridayRentSql = "SELECT amount FROM rent_settings WHERE day_type = 'friday' AND user_id = ? LIMIT 1";
$stmt = $conn->prepare($fridayRentSql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$fridayRentResult = $stmt->get_result();
$fridayRent = 200;
if ($fridayRentResult && $fridayRentResult->num_rows > 0) {
    $fridayRent = $fridayRentResult->fetch_assoc()['amount'];
}

// Get special day rent settings
$specialDaySql = "SELECT special_date, amount FROM rent_settings WHERE day_type = 'special' AND user_id = ? AND MONTH(special_date) = ? AND YEAR(special_date) = ?";
$stmt = $conn->prepare($specialDaySql);
$stmt->bind_param("iii", $_SESSION['user_id'], $currentMonth, $currentYear);
$stmt->execute();
$specialDayResult = $stmt->get_result();

$specialDayRent = [];
while($row = $specialDayResult->fetch_assoc()) {
    $day = date('j', strtotime($row['special_date']));
    $specialDayRent[$day] = $row['amount'];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>এক্সেল শীট ম্যানেজমেন্ট - প্রচেষ্টা অটো রিকশা</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="futuristic-background.css">
    <style>
        body {
            font-family: 'Noto Sans Bengali', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            padding-bottom: 50px;
        }
        
        .navbar {
            background-color: #dc3545;
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: white;
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .page-header {
            padding: 2rem 0;
            text-align: center;
            margin-bottom: 2rem;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.05);
        }
        
        .page-header h1 {
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 0.5rem;
        }
        
        .month-selector {
            background-color: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 15px rgba(0,0,0,0.05);
            margin-bottom: 2rem;
        }
        
        .form-select {
            padding: 0.75rem 1rem;
            border: 1px solid #e0e0e0;
            font-weight: 500;
            border-radius: 8px;
            font-size: 1.1rem;
        }
        
        .btn-primary {
            background-color: #dc3545;
            border-color: #dc3545;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            border-radius: 8px;
            font-size: 1.1rem;
        }
        
        .excel-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.05);
            margin-bottom: 2rem;
            border: none;
        }
        
        .card-header {
            border-bottom: 1px solid rgba(0,0,0,0.1);
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            font-size: 1.2rem;
            border-radius: 10px 10px 0 0 !important;
        }
        
        .excel-table {
            padding: 0.5rem;
        }
        
        .excel-table table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .excel-table th, .excel-table td {
            border: 1px solid #e0e0e0;
            padding: 0.75rem;
            text-align: center;
            vertical-align: middle;
        }
        
        .excel-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .excel-table .rickshaw-name {
            background-color: #f8f9fa;
            font-weight: 600;
            position: sticky;
            left: 0;
            z-index: 5;
            min-width: 200px;
            text-align: left;
            padding-left: 15px;
        }
        
        .attendance-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            font-size: 1.2rem;
            cursor: pointer;
            border: none;
            transition: transform 0.2s;
        }
        
        .attendance-btn:hover {
            transform: scale(1.1);
        }
        
        .present {
            background-color: #28a745;
            color: white;
        }
        
        .absent {
            background-color: #dc3545;
            color: white;
        }
        
        .no-record {
            background-color: #f8f9fa;
            border: 1px dashed #ced4da;
            color: #6c757d;
        }
        
        .friday {
            background-color: #fff3cd;
        }
        
        .special-day {
            background-color: #cff4fc;
        }
        
        .day-header {
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .day-subheader {
            font-size: 0.8rem;
            color: #6c757d;
            display: block;
        }
        
        .rent-display {
            display: block;
            margin-top: 8px;
            font-weight: 600;
            color: #495057;
        }
        
        .edit-rent-btn {
            display: block;
            margin: 5px auto 0;
            font-size: 0.8rem;
            padding: 2px 8px;
            border-radius: 4px;
        }
        
        .card-footer {
            border-top: 1px solid rgba(0,0,0,0.1);
            padding: 1.25rem 1.5rem;
            background-color: #f8f9fa;
            border-radius: 0 0 10px 10px !important;
        }
        
        .legend-item {
            display: inline-flex;
            align-items: center;
            margin-right: 1.5rem;
            margin-bottom: 0.5rem;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .save-btn {
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            border-radius: 8px;
        }
        
        .alert {
            border-radius: 10px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            font-weight: 500;
        }
        
        .summary-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.05);
            margin-bottom: 2rem;
            border: none;
        }
        
        .summary-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .footer {
            background-color: #f8d7da;
            color: #842029;
            padding: 1.5rem 0;
            text-align: center;
            margin-top: 3rem;
            border-top: 1px solid #f5c2c7;
            border-radius: 10px;
        }
        
        @media (max-width: 992px) {
            .excel-table {
                overflow-x: auto;
            }
        }
        
        /* Print styles - completely revised */
        @media print {
            @page {
                size: landscape;
                margin: 0.5cm;
            }
            
            body {
                font-size: 12px;
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            
            .navbar, .page-header, .month-selector, .no-print, .card-footer, .footer, .btn-secondary, .edit-rent-btn {
                display: none !important;
            }
            
            .container {
                width: 100% !important;
                max-width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
            }
            
            .excel-card {
                border: none !important;
                box-shadow: none !important;
                width: 100% !important;
                margin: 0 !important;
                padding: 0 !important;
            }
            
            .excel-table {
                overflow: visible !important;
                width: 100% !important;
                display: block !important;
            }
            
            .excel-table table {
                width: 100% !important;
                table-layout: fixed !important;
                border-collapse: collapse !important;
            }
            
            .excel-table th, .excel-table td {
                padding: 4px !important;
                border: 1px solid #000 !important;
                font-size: 10px !important;
                overflow: visible !important;
                width: auto !important;
            }
            
            .excel-table th.rickshaw-name, .excel-table td.rickshaw-name {
                position: static !important;
                left: auto !important;
                width: 150px !important;
            }
            
            .excel-table th {
                position: static !important;
                top: auto !important;
            }
            
            .print-header {
                display: block !important;
                text-align: center;
                margin-bottom: 20px;
            }
            
            .print-footer {
                display: block !important;
                text-align: center;
                margin-top: 20px;
                font-size: 10px;
            }
            
            .card-header {
                display: none !important;
            }
            
            .present {
                background-color: #28a745 !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            .absent {
                background-color: #dc3545 !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            .friday {
                background-color: #fff3cd !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            .special-day {
                background-color: #cff4fc !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            .attendance-btn {
                width: 20px !important;
                height: 20px !important;
                min-width: 20px !important;
                min-height: 20px !important;
                font-size: 10px !important;
                padding: 0 !important;
            }
            
            .rent-display {
                font-size: 9px !important;
            }
            
            .summary-card {
                page-break-before: always;
            }
        }
        
        .print-header, .print-footer {
            display: none;
        }

        /* Modal Enhancements */
        .modal-content.glass-card {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px) !important;
            border: 2px solid rgba(255, 215, 0, 0.3) !important;
            color: #333 !important;
        }

        .modal-header {
            background: linear-gradient(135deg, #ffd700, #ffeb3b) !important;
            color: #000 !important;
            border-bottom: 1px solid rgba(255, 215, 0, 0.3) !important;
        }

        .modal-body .form-control {
            background: rgba(255, 255, 255, 0.9) !important;
            border: 2px solid rgba(255, 215, 0, 0.3) !important;
            color: #333 !important;
        }

        .modal-body .form-control:focus {
            border-color: #ffd700 !important;
            box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25) !important;
        }

        .edit-rent-btn {
            background: linear-gradient(135deg, #ffd700, #ffeb3b) !important;
            border: none !important;
            color: #000 !important;
            font-weight: bold !important;
            transition: all 0.3s ease !important;
        }

        .edit-rent-btn:hover {
            background: linear-gradient(135deg, #ffeb3b, #ffd700) !important;
            transform: scale(1.1) !important;
            color: #000 !important;
        }
    </style>
</head>
<body>
    <!-- Futuristic 3D Background -->
    <?php include 'futuristic-background.html'; ?>

    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                প্রচেষ্টা অটো রিকশা
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link text-white" href="index.php">
                            <i class="bi bi-house-door me-1"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="rent_settings.php">
                            <i class="bi bi-gear me-1"></i> ভাড়া সেটিংস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="logout.php">
                            <i class="bi bi-box-arrow-right me-1"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <h1>
                <i class="bi bi-table me-2"></i>
                এক্সেল শীট ম্যানেজমেন্ট
            </h1>
            <p class="text-muted mb-0">সকল রিকশার উপস্থিতি এবং ভাড়া একসাথে ম্যানেজ করুন</p>
        </div>
        
        <?php if(isset($_GET['updated']) && $_GET['updated'] == 1): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>
            উপস্থিতি এবং ভাড়া সফলভাবে আপডেট করা হয়েছে।
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if(isset($_GET['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <?php
            switch($_GET['error']) {
                case 'invalid_date':
                    echo 'অবৈধ তারিখ। দয়া করে আবার চেষ্টা করুন।';
                    break;
                case 'database_error':
                    echo 'ডেটাবেস ত্রুটি। দয়া করে আবার চেষ্টা করুন।';
                    break;
                default:
                    echo 'একটি ত্রুটি ঘটেছে। দয়া করে আবার চেষ্টা করুন।';
            }
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>
        
        <!-- Month Selector -->
        <form class="month-selector" method="get">
            <div class="row g-3">
                <div class="col-md-5">
                    <label class="form-label fw-bold">মাস নির্বাচন করুন</label>
                    <select name="month" class="form-select">
                        <?php
                        for($m=1; $m<=12; $m++) {
                            $selected = ($m == $currentMonth) ? 'selected' : '';
                            $monthName = date('F', mktime(0, 0, 0, $m, 1));
                            echo "<option value='$m' $selected>$monthName</option>";
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label fw-bold">বছর নির্বাচন করুন</label>
                    <select name="year" class="form-select">
                        <?php
                        $startYear = 2023;
                        $endYear = 2100;
                        for($y=$startYear; $y<=$endYear; $y++) {
                            $selected = ($y == $currentYear) ? 'selected' : '';
                            echo "<option value='$y' $selected>$y</option>";
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search me-1"></i> দেখুন
                    </button>
                </div>
            </div>
        </form>
        
        <?php if(count($rickshaws) > 0): ?>
        <!-- Attendance Table -->
        <div class="card excel-card glass-card mb-4">
            <div class="print-header" style="display: none;">
                <h3>প্রচেষ্টা অটো রিকশা</h3>
                <h4>মাসিক উপস্থিতি এবং ভাড়া রিপোর্ট</h4>
                <h5><?php echo date('F Y', mktime(0, 0, 0, $currentMonth, 1, $currentYear)); ?></h5>
            </div>
            
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-calendar-check me-2"></i>
                        উপস্থিতি ম্যানেজমেন্ট (<?php echo date('F Y', mktime(0, 0, 0, $currentMonth, 1, $currentYear)); ?>)
                    </h5>
                    <div>
                        <button id="printExcelSheet" class="btn btn-light btn-sm">
                            <i class="bi bi-printer me-1"></i> প্রিন্ট করুন
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive excel-table">
                    <table class="table table-bordered m-0" id="attendanceTable">
                        <thead>
                            <tr>
                                <th class="text-center">রিকশা/ড্রাইভার</th>
                                <?php for($day = 1; $day <= $daysInMonth; $day++): ?>
                                    <?php 
                                    $date = sprintf("%04d-%02d-%02d", $currentYear, $currentMonth, $day);
                                    $dayOfWeek = date('w', strtotime($date));
                                    $dayName = date('D', strtotime($date));
                                    $isFriday = ($dayOfWeek == 5); // 5 is Friday
                                    $isSpecialDay = isset($specialDayRent[$day]);
                                    $thClass = $isFriday ? 'friday' : ($isSpecialDay ? 'special-day' : '');
                                    ?>
                                    <th class="<?php echo $thClass; ?>">
                                        <span class="day-header"><?php echo $day; ?></span>
                                        <span class="day-subheader"><?php echo $dayName; ?></span>
                                    </th>
                                <?php endfor; ?>
                                <th class="bg-info text-white">মোট ভাড়া</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $totalAllRickshaws = 0;
                            foreach($rickshaws as $rickshaw): 
                                $driverTotalRent = 0;
                            ?>
                            <tr>
                                <td class="rickshaw-name">
                                    <strong><?php echo $rickshaw['rickshaw_number']; ?></strong> - <?php echo $rickshaw['driver_name']; ?>
                                </td>
                                <?php for($day = 1; $day <= $daysInMonth; $day++): ?>
                                    <?php 
                                    $date = sprintf("%04d-%02d-%02d", $currentYear, $currentMonth, $day);
                                    $dayOfWeek = date('w', strtotime($date));
                                    $isFriday = ($dayOfWeek == 5); // 5 is Friday
                                    
                                    // Determine default rent for this day
                                    if(isset($specialDayRent[$day])) {
                                        $dayRent = $specialDayRent[$day];
                                    } else if($isFriday) {
                                        $dayRent = $fridayRent;
                                    } else {
                                        $dayRent = $defaultRent;
                                    }
                                    
                                    $status = '';
                                    $rent = $dayRent;
                                    
                                    if(isset($attendanceData[$rickshaw['driver_id']][$day])) {
                                        $status = $attendanceData[$rickshaw['driver_id']][$day]['status'];
                                        $rent = $attendanceData[$rickshaw['driver_id']][$day]['rent'];
                                        if($status == 'উপস্থিত') {
                                            $driverTotalRent += $rent;
                                        }
                                    }
                                    
                                    $btnClass = 'no-record';
                                    $btnIcon = '<i class="bi bi-dash"></i>';
                                    
                                    if($status == 'উপস্থিত') {
                                        $btnClass = 'present';
                                        $btnIcon = '<i class="bi bi-check-lg"></i>';
                                    } else if($status == 'অনুপস্থিত') {
                                        $btnClass = 'absent';
                                        $btnIcon = '<i class="bi bi-x-lg"></i>';
                                    }
                                    ?>
                                    <td>
                                        <a href="mark_attendance.php?driver_id=<?php echo $rickshaw['driver_id']; ?>&date=<?php echo $date; ?>&redirect=excel_attendance&month=<?php echo $currentMonth; ?>&year=<?php echo $currentYear; ?>"
                                           class="attendance-btn <?php echo $btnClass; ?>">
                                            <?php echo $btnIcon; ?>
                                        </a>
                                        
                                        <?php if($status == 'উপস্থিত'): ?>
                                        <span class="rent-display"><?php echo $rent; ?> ৳</span>
                                        <button type="button" class="btn btn-sm btn-secondary edit-rent-btn"
                                                data-bs-toggle="modal" data-bs-target="#editRentModal"
                                                data-driver-id="<?php echo $rickshaw['driver_id']; ?>"
                                                data-day="<?php echo $day; ?>"
                                                data-rent="<?php echo $rent; ?>"
                                                data-date="<?php echo $date; ?>">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <?php endif; ?>
                                    </td>
                                <?php endfor; ?>
                                <td class="fw-bold bg-light">
                                    <?php 
                                    echo number_format($driverTotalRent, 0) . " ৳"; 
                                    $totalAllRickshaws += $driverTotalRent;
                                    ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <tr class="bg-dark text-white">
                                <td class="text-end fw-bold">সর্বমোট ভাড়া</td>
                                <td colspan="<?php echo $daysInMonth; ?>" class="text-end fw-bold">
                                    মোট আয়:
                                </td>
                                <td class="fw-bold fs-5"><?php echo number_format($totalAllRickshaws, 0); ?> ৳</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div class="mb-2">
                        <div class="legend-item">
                            <div class="legend-color present"></div>
                            <span>উপস্থিত</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color absent"></div>
                            <span>অনুপস্থিত</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #fff3cd;"></div>
                            <span>শুক্রবার</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #cff4fc;"></div>
                            <span>বিশেষ দিন</span>
                        </div>
                    </div>
                    <div>
                        <a href="print_monthly_report.php?month=<?php echo $currentMonth; ?>&year=<?php echo $currentYear; ?>" class="btn btn-success" target="_blank">
                            <i class="bi bi-printer me-1"></i> মাসিক রিপোর্ট প্রিন্ট করুন
                        </a>
                    </div>
                </div>
            </div>
            <div class="print-footer" style="display: none;">
                <p>প্রিন্ট তারিখ: <?php echo date('d/m/Y h:i A'); ?></p>
                <p>ডেভেলপ করেছেন মাহতাব উদ্দিন আহমেদ</p>
            </div>
        </div>
        
        <!-- Monthly Summary -->
        <div class="card summary-card">
            <div class="card-header bg-success text-white">
                <i class="bi bi-bar-chart-fill me-2"></i>
                মাসিক সারাংশ (<?php echo date('F Y', mktime(0, 0, 0, $currentMonth, 1, $currentYear)); ?>)
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover summary-table">
                        <thead>
                            <tr>
                                <th>রিকশা নম্বর</th>
                                <th>ড্রাইভার</th>
                                <th>উপস্থিত দিন</th>
                                <th>অনুপস্থিত দিন</th>
                                <th>মোট ভাড়া</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($rickshaws as $rickshaw): 
                                $driver_id = $rickshaw['driver_id'];
                                $presentDays = 0;
                                $absentDays = 0;
                                $totalRent = 0;
                                
                                // Calculate summary for this driver
                                foreach($attendanceData[$driver_id] ?? [] as $day => $data) {
                                    if($data['status'] == 'উপস্থিত') {
                                        $presentDays++;
                                        $totalRent += $data['rent'];
                                    } else if($data['status'] == 'অনুপস্থিত') {
                                        $absentDays++;
                                    }
                                }
                            ?>
                            <tr>
                                <td><?php echo $rickshaw['rickshaw_number']; ?></td>
                                <td><?php echo $rickshaw['driver_name']; ?></td>
                                <td class="text-success fw-bold"><?php echo $presentDays; ?></td>
                                <td class="text-danger fw-bold"><?php echo $absentDays; ?></td>
                                <td class="fw-bold"><?php echo number_format($totalRent, 0); ?> ৳</td>
                            </tr>
                            <?php endforeach; ?>
                            <tr class="table-info">
                                <td colspan="4" class="text-end fw-bold">সর্বমোট ভাড়া:</td>
                                <td class="fw-bold"><?php echo number_format($totalAllRickshaws, 0); ?> ৳</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4 mb-5">
            <a href="index.php" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-1"></i> ড্যাশবোর্ডে ফিরে যান
            </a>
        </div>
        
        <!-- Edit Rent Modal -->
        <div class="modal fade" id="editRentModal" tabindex="-1" aria-labelledby="editRentModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content glass-card">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editRentModalLabel">ভাড়া পরিবর্তন করুন</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form method="post" action="">
                        <div class="modal-body">
                            <input type="hidden" name="update_rent" value="1">
                            <input type="hidden" id="driver_id" name="driver_id" value="">
                            <input type="hidden" id="day" name="day" value="">
                            <input type="hidden" id="date" name="date" value="">
                            
                            <div class="mb-3">
                                <label for="rent" class="form-label">ভাড়া (টাকা)</label>
                                <input type="number" class="form-control" id="rent" name="rent" min="0" step="1" required>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                            <button type="submit" class="btn btn-primary">সেভ করুন</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            কোন সক্রিয় রিকশা পাওয়া যায়নি। আগে রিকশা এবং ড্রাইভার যোগ করুন।
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Footer -->
    <div class="footer">
        <div class="container">
            <p>© <?php echo date('Y'); ?> প্রচেষ্টা অটো রিকশা। সর্বসত্ব সংরক্ষিত।</p>
            <div class="developer-credit">
                ডেভেলপ করেছেন মাহতাব উদ্দিন আহমেদ
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set up edit rent modal
        document.addEventListener('DOMContentLoaded', function() {
            const editRentModal = document.getElementById('editRentModal');
            if (editRentModal) {
                editRentModal.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const driverId = button.getAttribute('data-driver-id');
                    const day = button.getAttribute('data-day');
                    const rent = button.getAttribute('data-rent');
                    const date = button.getAttribute('data-date');

                    // Debug logging
                    console.log('Modal data:', {driverId, day, rent, date});

                    document.getElementById('driver_id').value = driverId || '';
                    document.getElementById('day').value = day || '';
                    document.getElementById('rent').value = rent || '';
                    document.getElementById('date').value = date || '';

                    // Additional validation
                    if (!date || date === '') {
                        console.warn('Date is empty, using fallback');
                        const currentMonth = <?php echo $currentMonth; ?>;
                        const currentYear = <?php echo $currentYear; ?>;
                        const fallbackDate = `${currentYear}-${String(currentMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                        document.getElementById('date').value = fallbackDate;
                    }
                });
            }
            
            // Print Excel Sheet functionality
            const printButton = document.getElementById('printExcelSheet');
            if (printButton) {
                printButton.addEventListener('click', function() {
                    // Save current scroll position
                    const scrollPos = window.scrollY;
                    
                    // Create a new window for printing
                    const printWindow = window.open('', '_blank');
                    
                    // Get the content to print
                    const excelCard = document.querySelector('.excel-card').cloneNode(true);
                    
                    // Remove any sticky positioning and other problematic styles
                    const stickyElements = excelCard.querySelectorAll('.rickshaw-name, th');
                    stickyElements.forEach(el => {
                        el.style.position = 'static';
                        el.style.left = 'auto';
                        el.style.top = 'auto';
                        el.style.zIndex = 'auto';
                    });
                    
                    // Remove edit buttons
                    const editButtons = excelCard.querySelectorAll('.edit-rent-btn');
                    editButtons.forEach(btn => btn.remove());
                    
                    // Make sure the print header is visible
                    const printHeader = excelCard.querySelector('.print-header');
                    if (printHeader) {
                        printHeader.style.display = 'block';
                    }
                    
                    // Make sure the print footer is visible
                    const printFooter = excelCard.querySelector('.print-footer');
                    if (printFooter) {
                        printFooter.style.display = 'block';
                    }
                    
                    // Hide the card header
                    const cardHeader = excelCard.querySelector('.card-header');
                    if (cardHeader) {
                        cardHeader.style.display = 'none';
                    }
                    
                    // Create the HTML content for the print window
                    const printContent = `
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>এক্সেল শীট প্রিন্ট - প্রচেষ্টা অটো রিকশা</title>
                            <meta charset="UTF-8">
                            <style>
                                @page {
                                    size: landscape;
                                    margin: 0.5cm;
                                }
                                body {
                                    font-family: Arial, sans-serif;
                                    font-size: 12px;
                                    margin: 0;
                                    padding: 10px;
                                }
                                table {
                                    width: 100%;
                                    border-collapse: collapse;
                                    table-layout: fixed;
                                }
                                th, td {
                                    border: 1px solid #000;
                                    padding: 4px;
                                    text-align: center;
                                    font-size: 10px;
                                }
                                .rickshaw-name {
                                    text-align: left;
                                    width: 150px;
                                }
                                .present {
                                    background-color: #28a745 !important;
                                    color: white !important;
                                    -webkit-print-color-adjust: exact !important;
                                    print-color-adjust: exact !important;
                                }
                                .absent {
                                    background-color: #dc3545 !important;
                                    color: white !important;
                                    -webkit-print-color-adjust: exact !important;
                                    print-color-adjust: exact !important;
                                }
                                .friday {
                                    background-color: #fff3cd !important;
                                    -webkit-print-color-adjust: exact !important;
                                    print-color-adjust: exact !important;
                                }
                                .special-day {
                                    background-color: #cff4fc !important;
                                    -webkit-print-color-adjust: exact !important;
                                    print-color-adjust: exact !important;
                                }
                                .print-header {
                                    text-align: center;
                                    margin-bottom: 20px;
                                }
                                .print-footer {
                                    text-align: center;
                                    margin-top: 20px;
                                    font-size: 10px;
                                }
                                .attendance-btn {
                                    width: 20px;
                                    height: 20px;
                                    display: inline-flex;
                                    align-items: center;
                                    justify-content: center;
                                    border-radius: 50%;
                                    margin: 0 auto;
                                }
                            </style>
                        </head>
                        <body>
                            ${excelCard.outerHTML}
                            <script>
                                window.onload = function() {
                                    window.print();
                                    setTimeout(function() {
                                        window.close();
                                    }, 500);
                                };
                            </script>
                        </body>
                        </html>
                    `;
                    
                    // Write to the new window and print
                    printWindow.document.open();
                    printWindow.document.write(printContent);
                    printWindow.document.close();
                });
            }
        });
    </script>
    <script>
        // Simple direct print function that should work with most security configurations
        function printExcelSheet() {
            // Create a print-specific stylesheet
            const style = document.createElement('style');
            style.innerHTML = `
                @media print {
                    @page {
                        size: landscape;
                        margin: 0.5cm;
                    }
                    
                    body * {
                        visibility: hidden;
                    }
                    
                    .excel-card, .excel-card * {
                        visibility: visible;
                    }
                    
                    .excel-card {
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 100%;
                    }
                    
                    .navbar, .page-header, .month-selector, .btn, .card-header, .card-footer, .footer {
                        display: none !important;
                    }
                    
                    .excel-table {
                        overflow: visible !important;
                    }
                    
                    .excel-table th, .excel-table td {
                        position: static !important;
                        left: auto !important;
                        top: auto !important;
                    }
                    
                    .print-header, .print-footer {
                        display: block !important;
                        text-align: center;
                    }
                    
                    .print-header {
                        margin-bottom: 20px;
                    }
                    
                    .print-footer {
                        margin-top: 20px;
                        font-size: 10px;
                    }
                }
            `;
            document.head.appendChild(style);
            
            // Print the page
            window.print();
            
            // Remove the style after printing
            setTimeout(() => {
                document.head.removeChild(style);
            }, 1000);
        }

        // Add the print button
        document.addEventListener('DOMContentLoaded', function() {
            const cardHeader = document.querySelector('.card-header');
            if (cardHeader) {
                const printBtn = document.createElement('button');
                printBtn.className = 'btn btn-light btn-sm';
                printBtn.innerHTML = '<i class="bi bi-printer-fill me-1"></i> প্রিন্ট করুন';
                printBtn.onclick = printExcelSheet;
                
                const btnContainer = cardHeader.querySelector('div > div');
                if (btnContainer) {
                    btnContainer.appendChild(printBtn);
                }
            }
        });
    </script>
</body>
</html>









