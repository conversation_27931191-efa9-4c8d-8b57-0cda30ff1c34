<?php
session_start();
require_once 'db_connect.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

$error = '';
$success = '';

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $rickshaw_number = trim($_POST['rickshaw_number']);
    $status = $_POST['status'];
    
    // Validate input
    if(empty($rickshaw_number)) {
        $error = "রিকশা নম্বর দিন";
    } else {
        // Check if rickshaw number already exists for this user
        $checkSql = "SELECT id FROM rickshaws WHERE rickshaw_number = ? AND user_id = ?";
        $stmt = $conn->prepare($checkSql);
        $stmt->bind_param("si", $rickshaw_number, $_SESSION['user_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if($result->num_rows > 0) {
            $error = "এই রিকশা নম্বর ইতিমধ্যে বিদ্যমান";
        } else {
            // Insert new rickshaw
            $sql = "INSERT INTO rickshaws (user_id, rickshaw_number, status) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("iss", $_SESSION['user_id'], $rickshaw_number, $status);
            
            if ($stmt->execute()) {
                $rickshaw_id = $conn->insert_id;
                // Redirect to add driver page with the new rickshaw pre-selected
                header("Location: add_driver.php?rickshaw_id=" . $rickshaw_id . "&success=1");
                exit();
            } else {
                $error = "Error: " . $stmt->error;
            }
        }
        $stmt->close();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>নতুন রিকশা যোগ করুন - প্রচেষ্টা অটো রিকশা</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&display=swap');
        
        :root {
            --purple-700: #3a006a;
            --purple-800: #2d003e;
            --purple-200: #e9d5ff;
            --white: #fff;
            --gray-100: #f3f4f6;
            --gray-500: #6b7280;
            --red-500: #ef4444;
            --green-500: #10b981;
        }

        body {
            font-family: 'Noto Sans Bengali', 'SolaimanLipi', Arial, sans-serif;
            background: linear-gradient(135deg, var(--purple-700) 0%, var(--purple-800) 100%);
            min-height: 100vh;
            color: var(--white);
            padding-top: 70px;
        }

        .main-navbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .navbar-brand {
            color: var(--white) !important;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 1rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            color: var(--white);
        }

        .card-header {
            background: linear-gradient(135deg, var(--purple-700), var(--purple-800));
            color: var(--white);
            font-weight: 600;
            border-radius: 1rem 1rem 0 0 !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .form-label {
            color: var(--purple-200);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: var(--white);
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--purple-200);
            box-shadow: 0 0 0 0.2rem rgba(233, 213, 255, 0.25);
            color: var(--white);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--purple-700), var(--purple-800));
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--purple-800), var(--purple-700));
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: var(--white);
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white);
            transform: translateY(-2px);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #fca5a5;
            border-radius: 0.5rem;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #86efac;
            border-radius: 0.5rem;
        }

        .footer {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .developer-credit {
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }

        .icon-box {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 2rem;
            height: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
            margin-right: 0.5rem;
        }

        /* Animation */
        .fade-in {
            animation: fadeIn 0.6s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 576px) {
            .container {
                padding: 0 1rem;
            }
            
            .card-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar main-navbar">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-truck me-2"></i>প্রচেষ্টা অটো রিকশা
            </a>
        </div>
    </nav>

    <div class="container mt-4 fade-in" style="max-width: 500px;">
        <div class="card">
            <div class="card-header text-center">
                <i class="bi bi-plus-circle-fill me-2"></i> নতুন রিকশা যোগ করুন
            </div>
            <div class="card-body">
                <?php if(!empty($error)): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if(!empty($success)): ?>
                    <div class="alert alert-success"><?php echo $success; ?></div>
                <?php endif; ?>
                
                <form method="post" action="">
                    <div class="mb-3">
                        <label for="rickshaw_number" class="form-label">
                            <i class="bi bi-hash me-1"></i>রিকশা নম্বর
                        </label>
                        <input type="text" class="form-control" id="rickshaw_number" name="rickshaw_number" 
                               placeholder="যেমন: ১০১, ২০৫, ABC-123" 
                               value="<?php echo isset($rickshaw_number) ? htmlspecialchars($rickshaw_number) : ''; ?>" required>
                        <small class="text-muted">রিকশার অনন্য নম্বর দিন</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">
                            <i class="bi bi-toggle-on me-1"></i>অবস্থা
                        </label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="Active" <?php echo (isset($status) && $status == 'Active') ? 'selected' : ''; ?>>সক্রিয়</option>
                            <option value="Inactive" <?php echo (isset($status) && $status == 'Inactive') ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                        </select>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>রিকশা যোগ করুন
                        </button>
                        <a href="add_driver.php" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-1"></i>ড্রাইভার যোগ করার পাতায় ফিরে যান
                        </a>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="bi bi-house me-1"></i>হোম পেজে যান
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="footer">
            <p>© <?php echo date('Y'); ?> প্রচেষ্টা অটো রিকশা। সর্বস্বত্ব সংরক্ষিত।</p>
            <p class="developer-credit">©মাহতাব উদ্দিন আহমেদ।</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
