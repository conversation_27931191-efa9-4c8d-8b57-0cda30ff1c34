<?php
session_start();
require_once 'db_connect.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

echo "<h2>Rent Update Test</h2>";

// Test parameters
$test_driver_id = isset($_GET['driver_id']) ? intval($_GET['driver_id']) : 0;
$test_date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
$test_rent = isset($_GET['rent']) ? floatval($_GET['rent']) : 200;

if ($test_driver_id > 0) {
    echo "<h3>Testing rent update for Driver ID: $test_driver_id, Date: $test_date, Rent: $test_rent</h3>";
    
    // Check if record exists
    $checkSql = "SELECT id, rent FROM attendance WHERE driver_id = ? AND user_id = ? AND date = ?";
    $stmt = $conn->prepare($checkSql);
    $stmt->bind_param("iis", $test_driver_id, $_SESSION['user_id'], $test_date);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo "<p>Record exists with ID: {$row['id']}, Current rent: {$row['rent']}</p>";
        
        // Update the record
        $updateSql = "UPDATE attendance SET rent = ? WHERE driver_id = ? AND user_id = ? AND date = ?";
        $stmt = $conn->prepare($updateSql);
        $stmt->bind_param("diis", $test_rent, $test_driver_id, $_SESSION['user_id'], $test_date);
        $updateResult = $stmt->execute();
        
        echo "<p>Update result: " . ($updateResult ? "SUCCESS" : "FAILED") . "</p>";
        echo "<p>Affected rows: " . $stmt->affected_rows . "</p>";
        
        // Verify the update
        $verifySql = "SELECT rent FROM attendance WHERE driver_id = ? AND user_id = ? AND date = ?";
        $stmt = $conn->prepare($verifySql);
        $stmt->bind_param("iis", $test_driver_id, $_SESSION['user_id'], $test_date);
        $stmt->execute();
        $verifyResult = $stmt->get_result();
        $verifyRow = $verifyResult->fetch_assoc();
        
        echo "<p>New rent value in database: {$verifyRow['rent']}</p>";
        
    } else {
        echo "<p>No record found for this driver and date</p>";
        
        // Create a new record
        $insertSql = "INSERT INTO attendance (user_id, driver_id, date, status, rent) VALUES (?, ?, ?, 'উপস্থিত', ?)";
        $stmt = $conn->prepare($insertSql);
        $stmt->bind_param("iisd", $_SESSION['user_id'], $test_driver_id, $test_date, $test_rent);
        $insertResult = $stmt->execute();
        
        echo "<p>Insert result: " . ($insertResult ? "SUCCESS" : "FAILED") . "</p>";
        echo "<p>Insert ID: " . $conn->insert_id . "</p>";
    }
}

// Show all drivers for testing
echo "<h3>Available Drivers:</h3>";
$driverSql = "SELECT d.id, d.name, r.rickshaw_number FROM drivers d 
              JOIN rickshaws r ON d.rickshaw_id = r.id 
              WHERE d.user_id = ?";
$stmt = $conn->prepare($driverSql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$driverResult = $stmt->get_result();

echo "<ul>";
while($driver = $driverResult->fetch_assoc()) {
    echo "<li>";
    echo "Driver ID: {$driver['id']}, Name: {$driver['name']}, Rickshaw: {$driver['rickshaw_number']} ";
    echo "<a href='?driver_id={$driver['id']}&date=" . date('Y-m-d') . "&rent=250'>Test Update</a>";
    echo "</li>";
}
echo "</ul>";

// Show recent attendance records
echo "<h3>Recent Attendance Records:</h3>";
$recentSql = "SELECT a.*, d.name FROM attendance a 
              JOIN drivers d ON a.driver_id = d.id 
              WHERE a.user_id = ? 
              ORDER BY a.date DESC, a.id DESC 
              LIMIT 10";
$stmt = $conn->prepare($recentSql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$recentResult = $stmt->get_result();

echo "<table border='1'>";
echo "<tr><th>ID</th><th>Driver</th><th>Date</th><th>Status</th><th>Rent</th></tr>";
while($record = $recentResult->fetch_assoc()) {
    echo "<tr>";
    echo "<td>{$record['id']}</td>";
    echo "<td>{$record['name']}</td>";
    echo "<td>{$record['date']}</td>";
    echo "<td>{$record['status']}</td>";
    echo "<td>{$record['rent']}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<p><a href='excel_attendance.php'>Back to Excel Attendance</a></p>";
?>
