<?php
session_start();
require_once 'db_connect.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

if (!isset($_GET['driver_id']) || !isset($_GET['month']) || !isset($_GET['year'])) {
    header("Location: index.php");
    exit();
}

$driver_id = $_GET['driver_id'];
$currentMonth = intval($_GET['month']);
$currentYear = intval($_GET['year']);

// Get driver details
$sql = "SELECT d.*, r.rickshaw_number 
        FROM drivers d 
        JOIN rickshaws r ON d.rickshaw_id = r.id 
        WHERE d.id = ? AND d.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ii", $driver_id, $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    header("Location: index.php");
    exit();
}

$driver = $result->fetch_assoc();

// Get attendance data for this driver for the selected month
$attendanceSql = "SELECT date, status, rent FROM attendance 
                 WHERE driver_id = ? AND user_id = ? AND MONTH(date) = ? AND YEAR(date) = ?
                 ORDER BY date";
$stmt = $conn->prepare($attendanceSql);
$stmt->bind_param("iiii", $driver_id, $_SESSION['user_id'], $currentMonth, $currentYear);
$stmt->execute();
$attendanceResult = $stmt->get_result();

$attendanceData = [];
$presentDays = 0;
$absentDays = 0;
$totalRent = 0;

while($row = $attendanceResult->fetch_assoc()) {
    $day = date('j', strtotime($row['date']));
    $attendanceData[$day] = [
        'status' => $row['status'],
        'rent' => $row['rent'],
        'date' => $row['date']
    ];
    
    if($row['status'] == 'উপস্থিত') {
        $presentDays++;
        $totalRent += $row['rent'];
    } else {
        $absentDays++;
    }
}

$daysInMonth = cal_days_in_month(CAL_GREGORIAN, $currentMonth, $currentYear);
$totalRecordedDays = $presentDays + $absentDays;
$noRecordDays = $daysInMonth - $totalRecordedDays;
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ড্রাইভার মাসিক রিপোর্ট - <?php echo $driver['name']; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="futuristic-background.css">
    <style>
        body {
            font-family: 'Noto Sans Bengali', Arial, sans-serif;
            background-color: #fff;
            color: #333;
            padding: 20px;
        }
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #ddd;
            padding-bottom: 20px;
        }
        .report-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-subtitle {
            font-size: 18px;
            color: #666;
        }
        .driver-info {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .summary-box {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        .attendance-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .attendance-table th, .attendance-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .attendance-table th {
            background-color: #f5f5f5;
        }
        .present {
            background-color: #d4edda;
            color: #155724;
        }
        .absent {
            background-color: #f8d7da;
            color: #721c24;
        }
        .no-print {
            margin-bottom: 20px;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                padding: 0;
                margin: 0;
                background: white !important;
            }
            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
                background: white !important;
            }
            /* Hide futuristic background elements during print */
            .futuristic-bg, .stars, .floating-shapes, .grid-lines, .particles {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- Futuristic 3D Background -->
    <?php include 'futuristic-background.html'; ?>

    <div class="container">
        <div class="no-print">
            <button onclick="window.print()" class="btn btn-primary mb-3">
                <i class="bi bi-printer"></i> প্রিন্ট করুন
            </button>
            <a href="index.php" class="btn btn-secondary mb-3">ফিরে যান</a>
        </div>
        
        <div class="report-header">
            <div class="report-title">প্রচেষ্টা অটো রিকশা</div>
            <div class="report-subtitle">ড্রাইভার মাসিক উপস্থিতি রিপোর্ট</div>
            <div><?php echo date('F Y', mktime(0, 0, 0, $currentMonth, 1, $currentYear)); ?></div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="driver-info">
                    <h5>ড্রাইভার তথ্য</h5>
                    <p><strong>নাম:</strong> <?php echo $driver['name']; ?></p>
                    <p><strong>রিকশা নম্বর:</strong> <?php echo $driver['rickshaw_number']; ?></p>
                    <p><strong>ফোন:</strong> <?php echo $driver['phone']; ?></p>
                    <p><strong>এনআইডি:</strong> <?php echo $driver['nid']; ?></p>
                    <p><strong>যোগদানের তারিখ:</strong> <?php echo $driver['join_date']; ?></p>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="summary-box">
                    <h5>উপস্থিতি সারাংশ</h5>
                    <div class="row mt-3">
                        <div class="col-4">
                            <div><strong><?php echo $presentDays; ?></strong></div>
                            <div>উপস্থিত</div>
                        </div>
                        <div class="col-4">
                            <div><strong><?php echo $absentDays; ?></strong></div>
                            <div>অনুপস্থিত</div>
                        </div>
                        <div class="col-4">
                            <div><strong><?php echo $noRecordDays; ?></strong></div>
                            <div>রেকর্ড নেই</div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <h4>মোট ভাড়া: <?php echo number_format($totalRent, 0); ?> টাকা</h4>
                    </div>
                </div>
            </div>
        </div>
        
        <h5 class="mt-4 mb-3">দৈনিক উপস্থিতি বিবরণ</h5>
        <table class="attendance-table">
            <thead>
                <tr>
                    <th>তারিখ</th>
                    <th>উপস্থিতি</th>
                    <th>ভাড়া (টাকা)</th>
                </tr>
            </thead>
            <tbody>
                <?php
                // Sort by date
                ksort($attendanceData);
                
                foreach($attendanceData as $day => $data) {
                    $date = date('d F Y', strtotime($data['date']));
                    $status = $data['status'];
                    $rent = $data['rent'];
                    $statusClass = ($status == 'উপস্থিত') ? 'present' : 'absent';
                    
                    echo "<tr class='$statusClass'>";
                    echo "<td>$date</td>";
                    echo "<td>$status</td>";
                    echo "<td>" . ($status == 'উপস্থিত' ? number_format($rent, 0) . ' ৳' : '-') . "</td>";
                    echo "</tr>";
                }
                
                if(count($attendanceData) == 0) {
                    echo "<tr><td colspan='3' class='text-center'>কোন রেকর্ড পাওয়া যায়নি</td></tr>";
                }
                ?>
            </tbody>
            <tfoot>
                <tr>
                    <th colspan="2">মোট ভাড়া</th>
                    <th><?php echo number_format($totalRent, 0); ?> টাকা</th>
                </tr>
            </tfoot>
        </table>
        
        <div class="mt-5 text-center">
            <p>প্রিন্ট তারিখ: <?php echo date('d/m/Y h:i A'); ?></p>
            <p>ডেভেলপ করেছেন মাহতাব উদ্দিন আহমেদ</p>
        </div>
    </div>
    
    <script>
        // Auto print when page loads
        window.onload = function() {
            // Uncomment the line below to automatically open print dialog
            // window.print();
        }
    </script>
</body>
</html>