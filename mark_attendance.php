<?php
session_start();
require_once 'db_connect.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

if (!isset($_GET['driver_id']) || !isset($_GET['date'])) {
    header("Location: index.php");
    exit();
}

$driver_id = $_GET['driver_id'];
$date = $_GET['date'];

// Validate date format
if (!preg_match("/^\d{4}-\d{1,2}-\d{1,2}$/", $date)) {
    header("Location: index.php");
    exit();
}

// Check if driver exists
$checkSql = "SELECT id FROM drivers WHERE id = ? AND user_id = ?";
$stmt = $conn->prepare($checkSql);
$stmt->bind_param("ii", $driver_id, $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    header("Location: index.php");
    exit();
}

// Get rent amount based on date
function getRentForDate($conn, $date, $user_id) {
    // Check if it's a special day
    $specialSql = "SELECT amount FROM rent_settings WHERE day_type = 'special' AND special_date = ? AND user_id = ?";
    $stmt = $conn->prepare($specialSql);
    $stmt->bind_param("si", $date, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return $result->fetch_assoc()['amount'];
    }

    // Check if it's Friday
    $dayOfWeek = date('w', strtotime($date));
    if ($dayOfWeek == 5) { // 5 is Friday
        $fridaySql = "SELECT amount FROM rent_settings WHERE day_type = 'friday' AND user_id = ?";
        $stmt = $conn->prepare($fridaySql);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result->num_rows > 0) {
            return $result->fetch_assoc()['amount'];
        }
    }

    // Regular day
    $regularSql = "SELECT amount FROM rent_settings WHERE day_type = 'regular' AND user_id = ?";
    $stmt = $conn->prepare($regularSql);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows > 0) {
        return $result->fetch_assoc()['amount'];
    }

    // Default if no settings found
    return 150.00;
}

// Check if attendance record already exists
$checkAttSql = "SELECT id, status FROM attendance WHERE driver_id = ? AND user_id = ? AND date = ?";
$stmt = $conn->prepare($checkAttSql);
$stmt->bind_param("iis", $driver_id, $_SESSION['user_id'], $date);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    // Record exists, toggle status
    $row = $result->fetch_assoc();
    $newStatus = ($row['status'] == 'উপস্থিত') ? 'অনুপস্থিত' : 'উপস্থিত';
    
    // Calculate rent based on new status
    $rent = ($newStatus == 'উপস্থিত') ? getRentForDate($conn, $date, $_SESSION['user_id']) : 0;
    
    $updateSql = "UPDATE attendance SET status = ?, rent = ? WHERE id = ? AND user_id = ?";
    $stmt = $conn->prepare($updateSql);
    $stmt->bind_param("sdii", $newStatus, $rent, $row['id'], $_SESSION['user_id']);
    $stmt->execute();
} else {
    // Record doesn't exist, create new with default 'উপস্থিত'
    $rent = getRentForDate($conn, $date, $_SESSION['user_id']);
    
    $insertSql = "INSERT INTO attendance (user_id, driver_id, date, status, rent) VALUES (?, ?, ?, 'উপস্থিত', ?)";
    $stmt = $conn->prepare($insertSql);
    $stmt->bind_param("iisd", $_SESSION['user_id'], $driver_id, $date, $rent);
    $stmt->execute();
}

// Redirect back to appropriate page
if(isset($_GET['redirect']) && $_GET['redirect'] == 'excel_attendance') {
    $month = isset($_GET['month']) ? $_GET['month'] : date('m');
    $year = isset($_GET['year']) ? $_GET['year'] : date('Y');
    header("Location: excel_attendance.php?month=$month&year=$year");
} else {
    // Default redirect to attendance page or referer
    $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'index.php';
    header("Location: $referer");
}
exit();
?>

