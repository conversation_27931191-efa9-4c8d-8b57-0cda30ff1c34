<?php
require_once 'db_connect.php';

// Process form submission for adding/updating rent settings
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action'])) {
    if ($_POST['action'] == 'update_regular') {
        $amount = $_POST['regular_amount'];
        $sql = "UPDATE rent_settings SET amount = ? WHERE day_type = 'regular' AND user_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("di", $amount, $_SESSION['user_id']);
        $stmt->execute();
    } 
    else if ($_POST['action'] == 'update_friday') {
        $amount = $_POST['friday_amount'];
        $sql = "UPDATE rent_settings SET amount = ? WHERE day_type = 'friday' AND user_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("di", $amount, $_SESSION['user_id']);
        $stmt->execute();
    }
    else if ($_POST['action'] == 'add_special') {
        $date = $_POST['special_date'];
        $name = $_POST['special_name'];
        $amount = $_POST['special_amount'];
        
        // Check if special date already exists
        $checkSql = "SELECT id FROM rent_settings WHERE day_type = 'special' AND user_id = ? AND special_date = ?";
        $stmt = $conn->prepare($checkSql);
        $stmt->bind_param("is", $_SESSION['user_id'], $date);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            // Update existing special date
            $row = $result->fetch_assoc();
            $sql = "UPDATE rent_settings SET special_name = ?, amount = ? WHERE id = ? AND user_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sdii", $name, $amount, $row['id'], $_SESSION['user_id']);
        } else {
            // Insert new special date
            $sql = "INSERT INTO rent_settings (user_id, day_type, special_date, special_name, amount) VALUES (?, 'special', ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("issd", $_SESSION['user_id'], $date, $name, $amount);
        }
        $stmt->execute();
    }
    else if ($_POST['action'] == 'delete_special' && isset($_POST['special_id'])) {
        $id = $_POST['special_id'];
        $sql = "DELETE FROM rent_settings WHERE id = ? AND day_type = 'special' AND user_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $id, $_SESSION['user_id']);
        $stmt->execute();
    }
    
    // Redirect to avoid form resubmission
    header("Location: rent_settings.php");
    exit();
}

// Get average driver rent as default
$avgRentSql = "SELECT AVG(rent) as avg_rent FROM drivers WHERE user_id = ?";
$stmt = $conn->prepare($avgRentSql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$avgResult = $stmt->get_result();
$defaultRent = ($avgResult->num_rows > 0) ? round($avgResult->fetch_assoc()['avg_rent'], 0) : 150.00;

// Get regular rent setting
$regularSql = "SELECT amount FROM rent_settings WHERE day_type = 'regular' AND user_id = ?";
$stmt = $conn->prepare($regularSql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
$regularAmount = ($result->num_rows > 0) ? $result->fetch_assoc()['amount'] : $defaultRent;

// Get Friday rent setting
$fridaySql = "SELECT amount FROM rent_settings WHERE day_type = 'friday' AND user_id = ?";
$stmt = $conn->prepare($fridaySql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
$fridayAmount = ($result->num_rows > 0) ? $result->fetch_assoc()['amount'] : ($defaultRent + 50);

// Get special day settings
$specialSql = "SELECT id, special_date, special_name, amount FROM rent_settings
               WHERE day_type = 'special' AND user_id = ? ORDER BY special_date DESC";
$stmt = $conn->prepare($specialSql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$specialResult = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ভাড়া সেটিংস</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --purple-50: #f5f3fa;
            --purple-100: #ede9fe;
            --purple-200: #c4b5fd;
            --purple-300: #a78bfa;
            --purple-400: #8b5cf6;
            --purple-500: #7c3aed;
            --purple-600: #6d28d9;
            --purple-700: #3a006a;
            --purple-800: #2d003e;
            --purple-900: #1e0026;
            --purple-950: #12001a;
            --white: #fff;
        }
        body {
            font-family: 'Noto Sans Bengali', 'SolaimanLipi', Arial, sans-serif;
            background: linear-gradient(135deg, var(--purple-700) 0%, var(--purple-800) 100%);
            color: var(--white);
            padding-top: 70px;
        }
        .main-navbar {
            background-color: var(--purple-700);
            box-shadow: 0 4px 6px -1px rgba(58, 0, 106, 0.1), 0 2px 4px -1px rgba(58, 0, 106, 0.06);
            padding: 0.75rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1030;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--white);
        }
        .navbar-brand:hover {
            color: var(--purple-200);
        }
        .footer {
            background-color: var(--purple-900);
            color: var(--purple-200);
            padding: 1.5rem 0;
            text-align: center;
            margin-top: 3rem;
            border-top: 1px solid var(--purple-700);
        }
        .footer p {
            margin-bottom: 0;
            font-weight: 500;
        }
        .developer-credit {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: var(--purple-400);
        }
        .card {
            border: none;
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.2s;
            background: linear-gradient(135deg, var(--purple-800) 60%, var(--purple-700) 100%);
            color: var(--white);
            margin-bottom: 1.5rem;
        }
        .card-header {
            padding: 1rem 1.25rem;
            border: none;
            font-weight: 600;
            background: linear-gradient(135deg, var(--purple-700), var(--purple-900));
            color: var(--white);
        }
        .btn-primary {
            background: linear-gradient(135deg, var(--purple-700), var(--purple-800));
            border-color: var(--purple-800);
            color: var(--white);
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, var(--purple-800), var(--purple-700));
            border-color: var(--purple-900);
        }
        .btn-success {
            background: linear-gradient(135deg, var(--purple-600), var(--purple-700));
            border-color: var(--purple-800);
            color: var(--white);
        }
        .btn-success:hover {
            background: linear-gradient(135deg, var(--purple-800), var(--purple-600));
            border-color: var(--purple-900);
        }
        .btn-info {
            background: linear-gradient(135deg, var(--purple-200), var(--purple-400));
            border-color: var(--purple-400);
            color: var(--purple-900);
        }
        .btn-info:hover {
            background: linear-gradient(135deg, var(--purple-400), var(--purple-200));
            border-color: var(--purple-700);
            color: var(--white);
        }
        .btn-secondary {
            background: linear-gradient(135deg, var(--purple-800), var(--purple-700));
            border-color: var(--purple-900);
            color: var(--white);
        }
        .btn-secondary:hover {
            background: linear-gradient(135deg, var(--purple-700), var(--purple-800));
            border-color: var(--purple-900);
        }
    </style>
</head>
<body>
    <nav class="navbar main-navbar">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                প্রচেষ্টা অটো রিকশা
            </a>
        </div>
    </nav>
    <div class="container mt-4">
        <h1 class="text-center mb-4 text-danger">ভাড়া সেটিংস</h1>
        
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">নিয়মিত দিনের ভাড়া</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <input type="hidden" name="action" value="update_regular">
                            <div class="mb-3">
                                <label for="regular_amount" class="form-label">ভাড়া (টাকা)</label>
                                <input type="number" step="0.01" class="form-control" id="regular_amount" name="regular_amount" value="<?php echo $regularAmount; ?>" required>
                            </div>
                            <button type="submit" class="btn btn-primary">আপডেট করুন</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">শুক্রবারের ভাড়া</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <input type="hidden" name="action" value="update_friday">
                            <div class="mb-3">
                                <label for="friday_amount" class="form-label">ভাড়া (টাকা)</label>
                                <input type="number" step="0.01" class="form-control" id="friday_amount" name="friday_amount" value="<?php echo $fridayAmount; ?>" required>
                            </div>
                            <button type="submit" class="btn btn-success">আপডেট করুন</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header bg-info">
                <h5 class="mb-0">বিশেষ দিনের ভাড়া</h5>
            </div>
            <div class="card-body">
                <form method="post" action="" class="mb-4">
                    <input type="hidden" name="action" value="add_special">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="special_date" class="form-label">তারিখ</label>
                            <input type="date" class="form-control" id="special_date" name="special_date" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="special_name" class="form-label">বিশেষ দিনের নাম</label>
                            <input type="text" class="form-control" id="special_name" name="special_name" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="special_amount" class="form-label">ভাড়া (টাকা)</label>
                            <input type="number" step="0.01" class="form-control" id="special_amount" name="special_amount" required>
                        </div>
                        <div class="col-md-2 mb-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-info w-100">যোগ করুন</button>
                        </div>
                    </div>
                </form>
                
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead class="table-danger">
                            <tr>
                                <th>তারিখ</th>
                                <th>বিশেষ দিনের নাম</th>
                                <th>ভাড়া (টাকা)</th>
                                <th>অ্যাকশন</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if ($specialResult->num_rows > 0) {
                                while($row = $specialResult->fetch_assoc()) {
                                    echo "<tr>";
                                    echo "<td>" . date('Y-m-d', strtotime($row['special_date'])) . "</td>";
                                    echo "<td>" . $row['special_name'] . "</td>";
                                    echo "<td>" . $row['amount'] . "</td>";
                                    echo "<td>
                                            <form method='post' action='' style='display:inline;'>
                                                <input type='hidden' name='action' value='delete_special'>
                                                <input type='hidden' name='special_id' value='" . $row['id'] . "'>
                                                <button type='submit' class='btn btn-sm btn-danger' onclick='return confirm(\"আপনি কি নিশ্চিত?\");'>মুছুন</button>
                                            </form>
                                          </td>";
                                    echo "</tr>";
                                }
                            } else {
                                echo "<tr><td colspan='4' class='text-center'>কোন বিশেষ দিন যোগ করা হয়নি</td></tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="text-center mb-4">
            <a href="index.php" class="btn btn-secondary">ড্যাশবোর্ডে ফিরে যান</a>
        </div>
    </div>
    
    <div class="footer">
        <p>© <?php echo date('Y'); ?> প্রচেষ্টা অটো রিকশা। সর্বস্বত্ব সংরক্ষিত।</p>
        <p class="developer-credit"> ©মাহতাব উদ্দিন আহমেদ।</p>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

